<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>e-Procurement</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* TH Sarabun New Font Face - Local Files */
        @font-face {
            font-family: 'TH Sarabun New';
            font-style: normal;
            font-weight: 300;
            font-display: swap;
            src: url('./fonts/THSarabunNew-Light.ttf') format('truetype'),
                 url('fonts/THSarabunNew-Light.ttf') format('truetype');
        }

        @font-face {
            font-family: 'TH Sarabun New';
            font-style: normal;
            font-weight: 400;
            font-display: swap;
            src: url('./fonts/THSarabunNew-Regular.ttf') format('truetype'),
                 url('fonts/THSarabunNew-Regular.ttf') format('truetype');
        }

        @font-face {
            font-family: 'TH Sarabun New';
            font-style: normal;
            font-weight: 500;
            font-display: swap;
            src: url('./fonts/THSarabunNew-Medium.ttf') format('truetype'),
                 url('fonts/THSarabunNew-Medium.ttf') format('truetype');
        }

        @font-face {
            font-family: 'TH Sarabun New';
            font-style: normal;
            font-weight: 600;
            font-display: swap;
            src: url('./fonts/THSarabunNew-SemiBold.ttf') format('truetype'),
                 url('fonts/THSarabunNew-SemiBold.ttf') format('truetype');
        }

        @font-face {
            font-family: 'TH Sarabun New';
            font-style: normal;
            font-weight: 700;
            font-display: swap;
            src: url('./fonts/THSarabunNew-Bold.ttf') format('truetype'),
                 url('fonts/THSarabunNew-Bold.ttf') format('truetype');
        }
        body {
            font-family: 'TH Sarabun New', 'Sarabun', sans-serif !important;
        }

        * {
            font-family: 'TH Sarabun New', 'Sarabun', sans-serif !important;
        }

        h1, h2, h3, h4, h5, h6, p, div, span, a, button {
            font-family: 'TH Sarabun New', 'Sarabun', sans-serif !important;
        }

        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-gray-100 p-8 font-sans">

    <!-- หน้าแรก: เลือกประเภทการดำเนินการ -->
    <div class="max-w-6xl mx-auto min-h-screen bg-white rounded-lg shadow-xl p-8">
        <h1 class="text-3xl font-bold text-center text-blue-700 mb-8 bg-gradient-to-r from-blue-100 to-green-200 text-white p-8 rounded-t-lg">
            ขั้นตอนการประกาศจัดซื้อจัดจ้าง ระบบ e-Procurement
        </h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- ตัวเลือกที่ 1: บันทึกผลการจัดซื้อจัดจ้าง -->
            <a href="record.html" class="option-card bg-blue-50 border-2 border-blue-200 rounded-lg p-6 cursor-pointer hover:border-blue-400 hover:shadow-lg transition-all duration-300 block text-decoration-none">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-blue-800 mb-2">บันทึกผลการจัดซื้อจัดจ้าง</h3>
                    <p class="text-gray-600">วงเงินไม่เกิน 100,000 บาท</p>
                </div>
            </a>
            
            <!-- ตัวเลือกที่ 2: การประกาศผู้ชนะรายไตรมาส -->
            <a href="announce.html" class="option-card bg-green-50 border-2 border-green-200 rounded-lg p-6 cursor-pointer hover:border-green-400 hover:shadow-lg transition-all duration-300 block text-decoration-none">
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-green-800 mb-2">การประกาศผู้ชนะรายไตรมาส</h3>
                    <p class="text-gray-600">ขออนุมัติและประกาศผู้ชนะ</p>
                </div>
            </a>
        </div>
    </div>

    <script>
        // ตรวจสอบการโหลดฟอนต์
        document.fonts.ready.then(function() {
            console.log('All fonts loaded successfully');

            // ตรวจสอบว่าฟอนต์ TH Sarabun New โหลดแล้วหรือไม่
            if (document.fonts.check('16px "TH Sarabun New"')) {
                console.log('TH Sarabun New font is loaded');
                document.body.style.fontFamily = '"TH Sarabun New", "Sarabun", sans-serif';
            } else {
                console.log('TH Sarabun New font failed to load, using fallback');
                document.body.style.fontFamily = '"Sarabun", sans-serif';
            }
        });

        // Force font loading
        const fontFace = new FontFace('TH Sarabun New', 'url(fonts/THSarabunNew-Regular.ttf)');
        fontFace.load().then(function(loadedFont) {
            document.fonts.add(loadedFont);
            console.log('Font loaded manually');
        }).catch(function(error) {
            console.error('Font loading failed:', error);
        });
    </script>

</body>
</html>
