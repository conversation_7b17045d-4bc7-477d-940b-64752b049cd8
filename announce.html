<!DOCTYPE html>
<html lang="th">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ขั้นตอนการประกาศผู้ชนะรายไตรมาส</title>
    <link
      href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
      rel="stylesheet"
    />
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
      /* TH Sarabun New Font Face - Local Files */
      @font-face {
        font-family: 'TH Sarabun New';
        font-style: normal;
        font-weight: 300;
        font-display: swap;
        src: url('fonts/THSarabunNew-Light.ttf') format('truetype');
      }

      @font-face {
        font-family: 'TH Sarabun New';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url('fonts/THSarabunNew-Regular.ttf') format('truetype');
      }

      @font-face {
        font-family: 'TH Sarabun New';
        font-style: normal;
        font-weight: 500;
        font-display: swap;
        src: url('fonts/THSarabunNew-Medium.ttf') format('truetype');
      }

      @font-face {
        font-family: 'TH Sarabun New';
        font-style: normal;
        font-weight: 600;
        font-display: swap;
        src: url('fonts/THSarabunNew-SemiBold.ttf') format('truetype');
      }

      @font-face {
        font-family: 'TH Sarabun New';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url('fonts/THSarabunNew-Bold.ttf') format('truetype');
      }

      body {
        font-family: 'TH Sarabun New', 'Sarabun', sans-serif !important;
      }
      .step-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
          0 4px 6px -2px rgba(0, 0, 0, 0.05);
      }

      /* สไตล์ปุ่มสไลด์รูป */
      .slide-btn {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 50%;
        color: white;
        font-size: 20px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
      }

      .slide-btn:hover {
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
      }

      .slide-btn:active {
        transform: translateY(-50%) scale(0.95);
      }

      .slide-btn.prev {
        left: -25px;
      }

      .slide-btn.next {
        right: -25px;
      }

      /* เอฟเฟกต์เมื่อ disabled */
      .slide-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: translateY(-50%) scale(0.9);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      /* สไตล์ตัวนับรูป */
      .image-counter {
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      /* ให้แน่ใจว่า hidden class ทำงาน */
      .hidden {
        display: none !important;
      }
    </style>
  </head>
  <body class="bg-gray-100 p-8 font-sans">
    <!-- หน้าการประกาศผู้ชนะรายไตรมาส -->
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-xl p-8 relative">
      <div class="text-center mb-8">
        <a
          href="index.html"
          class="absolute left-8 top-8 flex items-center gap-2 bg-gradient-to-r from-blue-100 to-green-200 hover:from-green-200 hover:to-blue-100 text-blue-700 hover:text-blue-900 px-4 py-2 rounded-full shadow transition-all duration-200 group"
        >
          <svg
            class="w-6 h-6 text-blue-500 group-hover:text-green-600 transition"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M3 12l9-9 9 9M4 10v10a1 1 0 001 1h3a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1h3a1 1 0 001-1V10"
            ></path>
          </svg>
          <span class="font-semibold hidden sm:inline">กลับหน้าแรก</span>
        </a>
        <h1 class="text-3xl font-bold text-green-700">
          ขั้นตอนการประกาศผู้ชนะรายไตรมาส
        </h1>
      </div>

      <div class="space-y-6">
        <div
          id="announce-step1"
          class="step-card bg-blue-50 border-l-4 border-blue-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            1
          </div>
          <div>
            <h3 class="text-xl font-semibold text-blue-800">
              ล็อกอินเข้าระบบ e-Procurement
            </h3>
            <p class="text-gray-700 mt-2">
              เข้าเว็บไซต์
              <a
                href="https://eprocurement.pea.co.th"
                class="text-blue-500"
                target="_blank"
                >https://eprocurement.pea.co.th</a
              >
              จากนั้นกรอกชื่อผู้ใช้งานและรหัสผ่านเพื่อล็อกอินระบบ
            </p>
          </div>
        </div>

        <div
          id="announce-step2"
          class="step-card bg-green-50 border-l-4 border-green-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            2
          </div>
          <div>
            <h3 class="text-xl font-semibold text-green-800">
              เลือกการประกาศจัดซื้อจัดจ้าง (รูปแบบอื่นๆ)
            </h3>
            <p class="text-gray-700 mt-2">
              เมื่อเข้าระบบแล้ว ให้คลิกที่ "<strong class="font-medium"
                >การประกาศจัดซื้อจัดจ้าง (รูปแบบอื่นๆ)</strong
              >"
            </p>
          </div>
        </div>

        <div
          id="announce-step3"
          class="step-card bg-yellow-50 border-l-4 border-yellow-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-yellow-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            3
          </div>
          <div>
            <h3 class="text-xl font-semibold text-yellow-800">
              เลือกเมนูบันทึกผลการจัดซื้อจัดจ้างวงเงินไม่เกิน 100,000 บาท
            </h3>
            <p class="text-gray-700 mt-2">
              ภายใต้เมนูการประกาศจัดซื้อจัดจ้าง ให้เลือก "<strong
                class="font-medium"
                >บันทึกผลการจัดซื้อจัดจ้างวงเงินไม่เกิน 100,000 บาท</strong
              >"
            </p>
          </div>
        </div>

        <div
          id="announce-step4"
          class="step-card bg-purple-50 border-l-4 border-purple-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            4
          </div>
          <div>
            <h3 class="text-xl font-semibold text-purple-800">
              เลือกเมนูขออนุมัติประกาศผู้ชนะรายไตรมาส
            </h3>
            <p class="text-gray-700 mt-2">
              จากเมนูบันทึกผลการจัดซื้อจัดจ้าง ให้เลือก "<strong
                class="font-medium"
                >ขออนุมัติประกาศผู้ชนะรายไตรมาส</strong
              >"
            </p>
          </div>
        </div>

        <div
          id="announce-step5"
          class="step-card bg-red-50 border-l-4 border-red-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-red-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            5
          </div>
          <div>
            <h3 class="text-xl font-semibold text-red-800">
              กรอกรายละเอียดและส่งอนุมัติ
            </h3>
            <p class="text-gray-700 mt-2">
              กรอกรายละเอียดในช่องที่มีสัญลักษณ์
              <span class="text-red-500 font-bold">* </span>ให้ครบถ้วน และกดปุ่ม
              "<strong class="font-medium">บันทึกและส่งอนุมัติ</strong>" <br>
               <strong class="font-semibold">กรณี</strong> สามารถตรวจสอบข้อมูลได้ โดยกดปุ่ม "<strong class="font-medium">เอกสารแนบ PDF</strong>" หรือ 
              "<strong class="font-medium">เอกสารแนบ Excel</strong>"
            </p>
          </div>
        </div>

        <div
          id="announce-step6"
          class="step-card bg-indigo-50 border-l-4 border-indigo-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-indigo-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            6
          </div>
          <div>
            <h3 class="text-xl font-semibold text-indigo-800">
              ผู้อนุมัติประกาศดำเนินการ
            </h3>
            <p class="text-gray-700 mt-2">
              ผู้อนุมัติประกาศดำเนินการตามขั้นตอนที่ 1-3 และเลือก "<strong
                class="font-medium"
                >ถาดงานเข้า</strong
              >"
            </p>
          </div>
        </div>

        <div
          id="announce-step7"
          class="step-card bg-pink-50 border-l-4 border-pink-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-pink-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            7
          </div>
          <div>
            <h3 class="text-xl font-semibold text-pink-800">
              ไปที่ "รายละเอียด"
            </h3>
            <p class="text-gray-700 mt-2">
              จากถาดงานเข้า ให้คลิกที่ "<strong class="font-medium"
                >พิจารณาประกาศผลการจัดซื้อจัดจ้างวงเงินต่ำกว่า 1 แสน</strong
              >" เพื่อดูข้อมูลการขออนุมัติ
            </p>
          </div>
        </div>

        <div
          id="announce-step8"
          class="step-card bg-green-50 border-l-4 border-green-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer"
        >
          <div
            class="flex-shrink-0 w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center text-xl font-bold"
          >
            8
          </div>
          <div>
            <h3 class="text-xl font-semibold text-green-800">
              เลือกอนุมัติประกาศหรือส่งคืนแก้ไข
            </h3>
            <p class="text-gray-700 mt-2">
              ตรวจสอบข้อมูลและเลือก "<strong class="font-medium"
                >อนุมัติประกาศ</strong
              >" หรือ "<strong class="font-medium">ส่งคืนแก้ไข</strong>"
              แล้วกดปุ่ม "<strong class="font-medium">บันทึก</strong>" <br>
              <strong class="font-semibold">กรณี</strong> "ส่งคืนแก้ไข" งานจะเข้าไปอยู่ที่ "ถาดงานเข้า" ของผู้ลงประกาศ
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Popup สำหรับแสดงรูปภาพ -->
    <div
      id="popup-overlay"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden"
    >
      <div class="bg-white p-8 rounded-lg shadow-lg max-w-5xl w-full relative">
        <button
          id="close-popup"
          class="absolute top-3 right-3 text-gray-600 hover:text-gray-900 text-2xl font-bold"
        >
          &times;
        </button>
        <h3 id="popup-title" class="text-2xl font-bold mb-4 text-center"></h3>
        <div class="flex items-center justify-center relative">
          <button id="prev-btn" class="slide-btn prev hidden">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 18L9 12L15 6"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
          <img
            id="popup-image"
            src=""
            alt="Step Image"
            class="w-full h-auto mb-4 rounded shadow-lg"
          />
          <button id="next-btn" class="slide-btn next hidden">
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9 18L15 12L9 6"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </button>
        </div>
        <p id="popup-description" class="text-gray-800 text-center mb-4"></p>
        <div id="image-counter" class="flex justify-center hidden">
          <div class="image-counter">
            <span id="current-image">1</span> / <span id="total-images">1</span>
          </div>
        </div>
      </div>
    </div>

    <script>
      // JavaScript for Pop-up functionality
      const stepCards = document.querySelectorAll(".step-card");
      const popupOverlay = document.getElementById("popup-overlay");
      const popupTitle = document.getElementById("popup-title");
      const popupImage = document.getElementById("popup-image");
      const popupDescription = document.getElementById("popup-description");
      const closePopupBtn = document.getElementById("close-popup");
      const prevBtn = document.getElementById("prev-btn");
      const nextBtn = document.getElementById("next-btn");
      const imageCounter = document.getElementById("image-counter");
      const currentImageSpan = document.getElementById("current-image");
      const totalImagesSpan = document.getElementById("total-images");

      const stepData = {
        "announce-step1": {
          title: "ล็อกอินเข้าระบบ e-Procurement",
          images: ["Image/001.png"],
          description: "หน้าจอเข้าระบบ e-Procurement",
        },
        "announce-step2": {
          title: "เลือกการประกาศจัดซื้อจัดจ้าง (รูปแบบอื่นๆ)",
          images: ["Image/002.png"],
          description: "หน้าจอเลือกการประกาศจัดซื้อจัดจ้าง",
        },
        "announce-step3": {
          title: "เลือกเมนูบันทึกผลการจัดซื้อจัดจ้างวงเงินไม่เกิน 100,000 บาท",
          images: ["Image/003.png"],
          description: "หน้าจอเลือกเมนูบันทึกผล",
        },
        "announce-step4": {
          title: "เลือกเมนูขออนุมัติประกาศผู้ชนะรายไตรมาส",
          images: ["Image/004-1.png"],
          description: "หน้าจอเลือกเมนูขออนุมัติ",
        },
        "announce-step5": {
          title: "กรอกรายละเอียดและส่งอนุมัติ",
          images: ["Image/005-3.png"],
          description: "หน้าจอกรอกข้อมูลและส่งอนุมัติ",
        },
        "announce-step6": {
          title: "ผู้อนุมัติประกาศดำเนินการ",
          images: ["Image/006-1.png"],
          description: "หน้าจอถาดงานเข้าของผู้อนุมัติ",
        },
        "announce-step7": {
          title: 'ไปที่ "รายละเอียด"',
          images: ["Image/007-1.png"],
          description: "หน้าจอรายละเอียดการขออนุมัติ",
        },
        "announce-step8": {
          title: "เลือกอนุมัติประกาศหรือส่งคืนแก้ไข",
          images: ["Image/008.png"],
          description: "หน้าจอการอนุมัติหรือส่งคืน",
        },
      };

      let currentImageIndex = 0;
      let currentStepImages = [];

      stepCards.forEach((card) => {
        card.addEventListener("click", () => {
          const stepId = card.id;
          const data = stepData[stepId];

          if (data) {
            currentStepImages = data.images;
            currentImageIndex = 0;

            popupTitle.textContent = data.title;
            popupDescription.textContent = data.description;

            // รีเซ็ตสถานะปุ่มก่อนเสมอ
            prevBtn.classList.add("hidden");
            nextBtn.classList.add("hidden");
            imageCounter.classList.add("hidden");

            // แสดงรูปภาพ
            updateImage();

            // ตรวจสอบจำนวนรูปและแสดงปุ่มเฉพาะเมื่อมีรูปตั้งแต่ 2 รูปขึ้นไป
            if (currentStepImages.length >= 2) {
              prevBtn.classList.remove("hidden");
              nextBtn.classList.remove("hidden");
              imageCounter.classList.remove("hidden");
              totalImagesSpan.textContent = currentStepImages.length;
              updateNavigationButtons();

              console.log(
                `Step ${stepId}: แสดงปุ่มสไลด์ (มี ${currentStepImages.length} รูป)`
              );
            } else {
              console.log(
                `Step ${stepId}: ซ่อนปุ่มสไลด์ (มี ${currentStepImages.length} รูป)`
              );
            }

            popupOverlay.classList.remove("hidden");
          }
        });
      });

      function updateImage() {
        if (currentStepImages.length > 0) {
          popupImage.src = currentStepImages[currentImageIndex];
          currentImageSpan.textContent = currentImageIndex + 1;

          // อัพเดทสถานะปุ่มเมื่อเปลี่ยนรูป (เฉพาะเมื่อมีรูปมากกว่า 1 รูป)
          if (currentStepImages.length >= 2) {
            updateNavigationButtons();
          } else {
            // ให้แน่ใจว่าปุ่มซ่อนเมื่อมีรูปเดียว
            prevBtn.classList.add("hidden");
            nextBtn.classList.add("hidden");
            imageCounter.classList.add("hidden");
          }
        }
      }

      // ฟังก์ชันสำหรับอัพเดทสถานะปุ่มนำทาง
      function updateNavigationButtons() {
        // รีเซ็ตสถานะปุ่มก่อน
        prevBtn.style.opacity = "1";
        prevBtn.style.cursor = "pointer";
        nextBtn.style.opacity = "1";
        nextBtn.style.cursor = "pointer";

        // ปุ่ม Previous - ลดความโปร่งใสเมื่ออยู่รูปแรก
        if (currentImageIndex === 0) {
          prevBtn.style.opacity = "0.5";
          prevBtn.style.cursor = "not-allowed";
        }

        // ปุ่ม Next - ลดความโปร่งใสเมื่ออยู่รูปสุดท้าย
        if (currentImageIndex === currentStepImages.length - 1) {
          nextBtn.style.opacity = "0.5";
          nextBtn.style.cursor = "not-allowed";
        }
      }

      prevBtn.addEventListener("click", () => {
        // ตรวจสอบว่าสามารถไปรูปก่อนหน้าได้หรือไม่ และมีรูปมากกว่า 1 รูป
        if (currentImageIndex > 0 && currentStepImages.length >= 2) {
          currentImageIndex--;
          updateImage();
        }
      });

      nextBtn.addEventListener("click", () => {
        // ตรวจสอบว่าสามารถไปรูปถัดไปได้หรือไม่ และมีรูปมากกว่า 1 รูป
        if (
          currentImageIndex < currentStepImages.length - 1 &&
          currentStepImages.length >= 2
        ) {
          currentImageIndex++;
          updateImage();
        }
      });

      closePopupBtn.addEventListener("click", () => {
        popupOverlay.classList.add("hidden");
      });

      popupOverlay.addEventListener("click", (e) => {
        if (e.target === popupOverlay) {
          popupOverlay.classList.add("hidden");
        }
      });
    </script>
  </body>
</html>
