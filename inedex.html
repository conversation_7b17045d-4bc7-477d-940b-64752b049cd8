<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ขั้นตอนการ e-Procurement</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Custom styles can be added here if needed, or inline Tailwind classes are sufficient */
        body {
            font-family: 'Sarabun', 'TH Sarabun New';
        }
        .step-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* สไตล์ปุ่มสไลด์รูป */
        .slide-btn {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
        }

        .slide-btn:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .slide-btn:active {
            transform: translateY(-50%) scale(0.95);
        }

        .slide-btn.prev {
            left: -25px;
        }

        .slide-btn.next {
            right: -25px;
        }

        /* เอฟเฟกต์เมื่อ disabled */
        .slide-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: translateY(-50%) scale(0.9);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* สไตล์ตัวนับรูป */
        .image-counter {
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* ให้แน่ใจว่า hidden class ทำงาน */
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body class="bg-gray-100 p-8 font-sans">

    <!-- หน้าแรก: เลือกประเภทการดำเนินการ -->
    <div id="main-menu" class="max-w-4xl mx-auto bg-white rounded-lg shadow-xl p-8">
        <h1 class="text-3xl font-bold text-center text-blue-700 mb-8">
            ระบบ e-Procurement
        </h1>
        <p class="text-center text-gray-600 mb-8">เลือกประเภทการดำเนินการที่ต้องการ</p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- ตัวเลือกที่ 1: บันทึกผลการจัดซื้อจัดจ้าง -->
            <div id="option-record" class="bg-blue-50 border-2 border-blue-200 rounded-lg p-6 cursor-pointer hover:border-blue-400 hover:shadow-lg transition-all duration-300">
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-blue-800 mb-2">บันทึกผลการจัดซื้อจัดจ้าง</h3>
                    <p class="text-gray-600">วงเงินไม่เกิน 100,000 บาท</p>
                </div>
            </div>

            <!-- ตัวเลือกที่ 2: การประกาศผู้ชนะรายไตรมาส -->
            <div id="option-announce" class="bg-green-50 border-2 border-green-200 rounded-lg p-6 cursor-pointer hover:border-green-400 hover:shadow-lg transition-all duration-300">
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-green-800 mb-2">การประกาศผู้ชนะรายไตรมาส</h3>
                    <p class="text-gray-600">ขออนุมัติและประกาศผู้ชนะ</p>
                </div>
            </div>
        </div>
    </div>

    <!-- หน้าบันทึกผลการจัดซื้อจัดจ้าง -->
    <div id="record-section" class="max-w-4xl mx-auto bg-white rounded-lg shadow-xl p-8 hidden">
        <div class="flex items-center mb-8">
            <button id="back-to-menu-1" class="text-blue-500 hover:text-blue-700 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <h1 class="text-3xl font-bold text-blue-700">
                ขั้นตอนการบันทึกผลการจัดซื้อจัดจ้าง <br> วงเงินไม่เกิน 100,000 บาท
            </h1>
        </div>

        <div class="space-y-6">
            
            <div id="step1" class="step-card bg-blue-50 border-l-4 border-blue-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center text-xl font-bold">1</div>
                <div>
                    <h2 class="text-xl font-semibold text-blue-800">ล็อกอินเข้าระบบ e-Procurement</h2>
                    <p class="text-gray-700 mt-2">
                        เข้าสู่เว็บไซต์ <a href="https://eprocurement.pea.co.th" class="text-blue-500 " target="_blank">eprocurement.pea.co.th</a>
                        จากนั้นกรอกชื่อผู้ใช้งานและรหัสผ่านเพื่อล็อกอินเข้าระบบ
                    </p>
                </div>
            </div>

            <div id="step2" class="step-card bg-green-50 border-l-4 border-green-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center text-xl font-bold">2</div>
                <div>
                    <h2 class="text-xl font-semibold text-green-800">เลือกการประกาศจัดซื้อจัดจ้าง (รูปแบบอื่นๆ)</h2>
                    <p class="text-gray-700 mt-2">
                        เมื่อเข้าสู่ระบบได้แล้ว ให้คลิกที่เมนู "<strong class="font-medium">การประกาศจัดซื้อจัดจ้าง (รูปแบบอื่นๆ)</strong>"
                    </p>
                </div>
            </div>

            <div id="step3" class="step-card bg-yellow-50 border-l-4 border-yellow-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-yellow-500 text-white rounded-full flex items-center justify-center text-xl font-bold">3</div>
                <div>
                    <h2 class="text-xl font-semibold text-yellow-800">เลือกเมนูบันทึกผลการจัดซื้อจัดจ้างวงเงินไม่เกิน 100,000 บาท</h2>
                    <p class="text-gray-700 mt-2">
                        ภายใต้เมนูที่เลือกในขั้นตอนที่ 2 ให้เลือก "<strong class="font-medium">บันทึกผลการจัดซื้อจัดจ้างวงเงินไม่เกิน 100,000 บาท</strong>"
                    </p>
                </div>
            </div>

            <div id="step4" class="step-card bg-purple-50 border-l-4 border-purple-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center text-xl font-bold">4</div>
                <div>
                    <h2 class="text-xl font-semibold text-purple-800">เลือกเมนูเพิ่มผลการจัดซื้อจัดจ้างวงเงินไม่เกิน 100,000 บาท</h2>
                    <p class="text-gray-700 mt-2">
                        จากเมนูที่ปรากฏขึ้น ให้คลิกที่ "<strong class="font-medium">เพิ่มผลการจัดซื้อจัดจ้างวงเงินไม่เกิน 100,000 บาท</strong>"
                    </p>
                </div>
            </div>

            <div id="step5" class="step-card bg-red-50 border-l-4 border-red-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-red-500 text-white rounded-full flex items-center justify-center text-xl font-bold">5</div>
                <div>
                    <h2 class="text-xl font-semibold text-red-800">กดปุ่ม "เพิ่ม" กรอกรายละเอียด และ "บันทึก"</h2>
                    <p class="text-gray-700 mt-2">
                        กดปุ่ม "<strong class="font-medium">เพิ่ม</strong>" จากนั้นกรอกรายละเอียดในช่องที่มีสัญลักษณ์ <span class="text-red-500 font-bold">*</span> ให้ครบถ้วน เมื่อเสร็จแล้วให้กดปุ่ม "<strong class="font-medium">บันทึก</strong>"
                    </p>
                </div>
            </div>

        </div>

        <!-- ขั้นตอนการแก้ไขและลบข้อมูล -->
        <div class="mt-8 pt-8 border-t border-gray-200">
            <h2 class="text-2xl font-bold text-center text-green-700 mb-6">
                ขั้นตอนการแก้ไขและลบข้อมูล
            </h2>

            <div class="space-y-6">

                <div id="step6" class="step-card bg-indigo-50 border-l-4 border-indigo-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                    <div class="flex-shrink-0 w-10 h-10 bg-indigo-500 text-white rounded-full flex items-center justify-center text-xl font-bold">1</div>
                    <div>
                        <h3 class="text-xl font-semibold text-indigo-800">เลือกเมนูเพิ่มผลการจัดซื้อจัดจ้างวงเงินไม่เกิน 100,000 บาท</h3>
                        <p class="text-gray-700 mt-2">
                            เข้าไปที่เมนู "<strong class="font-medium">เพิ่มผลการจัดซื้อจัดจ้างวงเงินไม่เกิน 100,000 บาท</strong>" เพื่อดูรายการข้อมูลที่มีอยู่
                        </p>
                    </div>
                </div>

                <div id="step7" class="step-card bg-orange-50 border-l-4 border-orange-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                    <div class="flex-shrink-0 w-10 h-10 bg-orange-500 text-white rounded-full flex items-center justify-center text-xl font-bold">2</div>
                    <div>
                        <h3 class="text-xl font-semibold text-orange-800">เลือกเมนู "แก้ไข" หรือ "ลบ" ได้เลย</h3>
                        <p class="text-gray-700 mt-2">
                            จากรายการข้อมูลที่แสดง สามารถเลือกปุ่ม "<strong class="font-medium">แก้ไข</strong>" เพื่อแก้ไขข้อมูล หรือปุ่ม "<strong class="font-medium">ลบ</strong>" เพื่อลบข้อมูลได้ทันที
                        </p>
                    </div>
                </div>

                <!-- หมายเหตุ -->
                <div class="bg-yellow-50 border-l-4 border-yellow-400 rounded-lg p-6 mt-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h4 class="text-lg font-semibold text-yellow-800">หมายเหตุ</h4>
                            <div class="mt-2 text-gray-700">
                                <p>1. การแก้ไขและลบข้อมูลจะกระทำไม่ได้เมื่อดำเนินการขออนุมัติประกาศผู้ชนะรายไตรมาสนั้นๆ ไปแล้ว</p>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- หน้าการประกาศผู้ชนะรายไตรมาส -->
    <div id="announce-section" class="max-w-4xl mx-auto bg-white rounded-lg shadow-xl p-8 hidden flex flex-col">
        <div class="flex items-center mb-8">
            <button id="back-to-menu-2" class="text-green-500 hover:text-green-700 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <h1 class="text-3xl font-bold text-green-700">
                ขั้นตอนการประกาศผู้ชนะรายไตรมาส
            </h1>
        </div>

        <div class="space-y-6">

            <div id="announce-step1" class="step-card bg-blue-50 border-l-4 border-blue-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center text-xl font-bold">1</div>
                <div>
                    <h3 class="text-xl font-semibold text-blue-800">ล็อกอินเข้าระบบ e-Procurement</h3>
                    <p class="text-gray-700 mt-2">
                        เข้าเว็บไซต์ <a href="https://eprocurement.pea.co.th" class="text-blue-500" target="_blank">eprocurement.pea.co.th</a>
                        จากนั้นกรอกชื่อผู้ใช้งานและรหัสผ่านเพื่อล็อกอินระบบ
                    </p>
                </div>
            </div>

            <div id="announce-step2" class="step-card bg-green-50 border-l-4 border-green-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center text-xl font-bold">2</div>
                <div>
                    <h3 class="text-xl font-semibold text-green-800">เลือกการประกาศจัดซื้อจัดจ้าง (รูปแบบอื่นๆ)</h3>
                    <p class="text-gray-700 mt-2">
                        เมื่อเข้าระบบแล้ว ให้คลิกที่ "<strong class="font-medium">การประกาศจัดซื้อจัดจ้าง (รูปแบบอื่นๆ)</strong>"
                    </p>
                </div>
            </div>

            <div id="announce-step3" class="step-card bg-yellow-50 border-l-4 border-yellow-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-yellow-500 text-white rounded-full flex items-center justify-center text-xl font-bold">3</div>
                <div>
                    <h3 class="text-xl font-semibold text-yellow-800">เลือกเมนูบันทึกผลการจัดซื้อจัดจ้างวงเงินไม่เกิน 100,000 บาท</h3>
                    <p class="text-gray-700 mt-2">
                        ภายใต้เมนูการประกาศจัดซื้อจัดจ้าง ให้เลือก "<strong class="font-medium">บันทึกผลการจัดซื้อจัดจ้างวงเงินไม่เกิน 100,000 บาท</strong>"
                    </p>
                </div>
            </div>

            <div id="announce-step4" class="step-card bg-purple-50 border-l-4 border-purple-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center text-xl font-bold">4</div>
                <div>
                    <h3 class="text-xl font-semibold text-purple-800">เลือกเมนูขออนุมัติประกาศผู้ชนะรายไตรมาส</h3>
                    <p class="text-gray-700 mt-2">
                        จากเมนูบันทึกผลการจัดซื้อจัดจ้าง ให้เลือก "<strong class="font-medium">ขออนุมัติประกาศผู้ชนะรายไตรมาส</strong>"
                    </p>
                </div>
            </div>


            <div id="announce-step5" class="step-card bg-red-50 border-l-4 border-red-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-red-500 text-white rounded-full flex items-center justify-center text-xl font-bold">5</div>
                <div>
                    <h3 class="text-xl font-semibold text-red-800">กรอกรายละเอียดและส่งอนุมัติ</h3>
                    <p class="text-gray-700 mt-2">
                        กรอกรายละเอียดในช่องที่มีสัญลักษณ์ <span class="text-red-500 font-bold">*</span> ให้ครบถ้วน และกดปุ่ม "<strong class="font-medium">บันทึกและส่งอนุมัติ</strong>"
                    </p>
                </div>
            </div>

            <div id="announce-step6" class="step-card bg-indigo-50 border-l-4 border-indigo-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-indigo-500 text-white rounded-full flex items-center justify-center text-xl font-bold">6</div>
                <div>
                    <h3 class="text-xl font-semibold text-indigo-800">ผู้อนุมัติประกาศดำเนินการ</h3>
                    <p class="text-gray-700 mt-2">
                        ผู้อนุมัติประกาศดำเนินการตามขั้นตอนที่ 1-3 และเลือก "<strong class="font-medium">ถาดงานเข้า</strong>"
                    </p>
                </div>
            </div>

            <div id="announce-step7" class="step-card bg-pink-50 border-l-4 border-pink-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-pink-500 text-white rounded-full flex items-center justify-center text-xl font-bold">7</div>
                <div>
                    <h3 class="text-xl font-semibold text-pink-800">ไปที่ "รายละเอียด"</h3>
                    <p class="text-gray-700 mt-2">
                        จากถาดงานเข้า ให้คลิกที่ "<strong class="font-medium">รายละเอียด</strong>" เพื่อดูข้อมูลการขออนุมัติ
                    </p>
                </div>
            </div>

            <div id="announce-step8" class="step-card bg-teal-50 border-l-4 border-teal-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-teal-500 text-white rounded-full flex items-center justify-center text-xl font-bold">8</div>
                <div>
                    <h3 class="text-xl font-semibold text-teal-800">เลือกอนุมัติประกาศหรือส่งคืนแก้ไข</h3>
                    <p class="text-gray-700 mt-2">
                        ตรวจสอบข้อมูลและเลือก "<strong class="font-medium">อนุมัติประกาศ</strong>" หรือ "<strong class="font-medium">ส่งคืนแก้ไข</strong>" แล้วกดปุ่ม "<strong class="font-medium">บันทึก</strong>"
                    </p>
                </div>
            </div>

        </div>
    </div>
    <!-- <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-xl p-8 mt-8">
        <h1 class="text-3xl font-bold text-center text-green-700 mb-8">
            ขั้นตอนการแก้ไขและลบข้อมูล
        </h1>

        <div class="space-y-6">

            <div id="step6" class="step-card bg-indigo-50 border-l-4 border-indigo-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-indigo-500 text-white rounded-full flex items-center justify-center text-xl font-bold">1</div>
                <div>
                    <h2 class="text-xl font-semibold text-indigo-800">เลือกเมนูเพิ่มผลการจัดซื้อจัดจ้างวงเงินไม่เกิน 100,000 บาท</h2>
                    <p class="text-gray-700 mt-2">
                        เข้าไปที่เมนู "<strong class="font-medium">เพิ่มผลการจัดซื้อจัดจ้างวงเงินไม่เกิน 100,000 บาท</strong>" เพื่อดูรายการข้อมูลที่มีอยู่
                    </p>
                </div>
            </div>

            <div id="step7" class="step-card bg-pink-50 border-l-4 border-pink-500 rounded-lg p-6 flex items-start space-x-4 transition-all duration-300 ease-in-out cursor-pointer">
                <div class="flex-shrink-0 w-10 h-10 bg-pink-500 text-white rounded-full flex items-center justify-center text-xl font-bold">2</div>
                <div>
                    <h2 class="text-xl font-semibold text-pink-800">เลือกเมนู "แก้ไข" หรือ "ลบ" ได้เลย</h2>
                    <p class="text-gray-700 mt-2">
                        จากรายการข้อมูลที่แสดง สามารถเลือกปุ่ม "<strong class="font-medium">แก้ไข</strong>" เพื่อแก้ไขข้อมูล หรือปุ่ม "<strong class="font-medium">ลบ</strong>" เพื่อลบข้อมูลได้ทันที
                    </p>
                </div>
            </div> -->

            <!-- หมายเหตุ -->
            <!-- <div class="bg-red-500  p-4 rounded-lg">
                <div class="flex items-start">
                    <div class="flex-shrink-0 ">
                        <svg class="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-semibold ">หมายเหตุ</h3>
                        <div class="mt-2 ">
                            <p>1. การแก้ไขและลบข้อมูลจะกระทำไม่ได้ เมื่อดำเนินการขออนุมัติประกาศผู้ชนะรายไตรมาสนั้นๆ ไปแล้ว</p>
                            <p>2. เมื่อบันทึกผลการจัดซื้อจัดจ้างวงเงินไม่เกิน 100,000 บาท ข้อมูลจะปรากฎในรายงาน สขร.1 ทันที</p>
                        </div>
                    </div>
                </div>
            </div> -->

        </div>
    </div>

    <div id="popup-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white p-8 rounded-lg shadow-lg max-w-5xl w-full relative">
            <button id="close-popup" class="absolute top-3 right-3 text-gray-600 hover:text-gray-900 text-2xl font-bold">&times;</button>
            <h3 id="popup-title" class="text-2xl font-bold mb-4 text-center"></h3>
            <div class="flex items-center justify-center relative">
                <button id="prev-btn" class="slide-btn prev hidden">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
                <img id="popup-image" src="" alt="Step Image" class="w-full h-auto mb-4 rounded shadow-lg">
                <button id="next-btn" class="slide-btn next hidden">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>
            <p id="popup-description" class="text-gray-800 text-center mb-4"></p>
            <div id="image-counter" class="flex justify-center hidden">
                <div class="image-counter">
                    <span id="current-image">1</span> / <span id="total-images">1</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // การจัดการหน้าหลัก
        const mainMenu = document.getElementById('main-menu');
        const recordSection = document.getElementById('record-section');
        const announceSection = document.getElementById('announce-section');
        const optionRecord = document.getElementById('option-record');
        const optionAnnounce = document.getElementById('option-announce');
        const backToMenu1 = document.getElementById('back-to-menu-1');
        const backToMenu2 = document.getElementById('back-to-menu-2');

        // แสดงหน้าบันทึกผลการจัดซื้อจัดจ้าง
        optionRecord.addEventListener('click', () => {
            mainMenu.classList.add('hidden');
            recordSection.classList.remove('hidden');
            announceSection.classList.add('hidden');
        });

        // แสดงหน้าการประกาศผู้ชนะรายไตรมาส
        optionAnnounce.addEventListener('click', () => {
            mainMenu.classList.add('hidden');
            recordSection.classList.add('hidden');
            announceSection.classList.remove('hidden');
        });

        // กลับไปหน้าหลัก
        backToMenu1.addEventListener('click', () => {
            mainMenu.classList.remove('hidden');
            recordSection.classList.add('hidden');
            announceSection.classList.add('hidden');
        });

        backToMenu2.addEventListener('click', () => {
            mainMenu.classList.remove('hidden');
            recordSection.classList.add('hidden');
            announceSection.classList.add('hidden');
        });

        // JavaScript for Pop-up functionality
        const stepCards = document.querySelectorAll('.step-card');
        const popupOverlay = document.getElementById('popup-overlay');
        const popupTitle = document.getElementById('popup-title');
        const popupImage = document.getElementById('popup-image');
        const popupDescription = document.getElementById('popup-description');
        const closePopupBtn = document.getElementById('close-popup');
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        const imageCounter = document.getElementById('image-counter');
        const currentImageSpan = document.getElementById('current-image');
        const totalImagesSpan = document.getElementById('total-images');

        const stepData = {
            step1: {
                title: 'ล็อกอินระบบ e-Procurement',
                images: ['Image/001.png'],
                description: 'หน้าจอเข้าระบบ e-Procurement'
            },
            step2: {
                title: 'การประกาศซื้อจ้าง (อื้นๆ)',
                images: ['Image/002.png'],
                description: 'การประกาศซื้อจ้าง'
            },
            step3: {
                title: 'วงเงินไม่เกิน 100,000 บาท',
                images: ['Image/003.png'],
                description: 'หน้าจอผล'
            },
            step4: {
                title: 'วงเงินไม่เกิน 100,000 บาท',
                images: ['Image/004.png'],
                description: 'หน้าจอผล'
            },
            step5: {
                title: 'กรอกรายละเอียดและผล',
                images: ['Image/005.1.png', 'Image/005.2.png'],
                description: 'หน้าจอกรอกข้อมูลและปุ่ม'
            },
            step6: {
                title: 'เลือกเมนูเพิ่มผลการจัดซื้อจัดจ้างวงเงินไม่เกิน 100,000 บาท',
                images: ['Image/004.png'],
                description: 'หน้าจอแสดงเมนูเพิ่มผลการจัดซื้อจัดจ้าง'
            },
            step7: {
                title: 'เลือกเมนู "แก้ไข" หรือ "ลบ" ได้เลย',
                images: ['Image/007.png'],
                description: 'หน้าจอแสดงปุ่มแก้ไขและลบข้อมูล'
            },
            // ขั้นตอนการประกาศผู้ชนะรายไตรมาส
            'announce-step1': {
                title: 'ล็อกอินเข้าระบบ e-Procurement',
                images: ['Image/announce-001.png'],
                description: 'หน้าจอเข้าระบบ e-Procurement'
            },
            'announce-step2': {
                title: 'เลือกการประกาศจัดซื้อจัดจ้าง (รูปแบบอื่นๆ)',
                images: ['Image/announce-002.png'],
                description: 'หน้าจอเลือกการประกาศจัดซื้อจัดจ้าง'
            },
            'announce-step3': {
                title: 'เลือกเมนูบันทึกผลการจัดซื้อจัดจ้างวงเงินไม่เกิน 100,000 บาท',
                images: ['Image/announce-003.png'],
                description: 'หน้าจอเลือกเมนูบันทึกผล'
            },
            'announce-step4': {
                title: 'เลือกเมนูขออนุมัติประกาศผู้ชนะรายไตรมาส',
                images: ['Image/announce-004.png'],
                description: 'หน้าจอเลือกเมนูขออนุมัติ'
            },
            'announce-step5': {
                title: 'กรอกรายละเอียดและส่งอนุมัติ',
                images: ['Image/announce-005.png'],
                description: 'หน้าจอกรอกข้อมูลและส่งอนุมัติ'
            },
            'announce-step6': {
                title: 'ผู้อนุมัติประกาศดำเนินการ',
                images: ['Image/announce-006.png'],
                description: 'หน้าจอถาดงานเข้าของผู้อนุมัติ'
            },
            'announce-step7': {
                title: 'ไปที่ "รายละเอียด"',
                images: ['Image/announce-007.png'],
                description: 'หน้าจอรายละเอียดการขออนุมัติ'
            },
            'announce-step8': {
                title: 'เลือกอนุมัติประกาศหรือส่งคืนแก้ไข',
                images: ['Image/announce-008.png'],
                description: 'หน้าจอการอนุมัติหรือส่งคืน'
            }
        };

        let currentImageIndex = 0;
        let currentStepImages = [];

        stepCards.forEach(card => {
            card.addEventListener('click', () => {
                const stepId = card.id;
                const data = stepData[stepId];

                if (data) {
                    currentStepImages = data.images;
                    currentImageIndex = 0;

                    popupTitle.textContent = data.title;
                    popupDescription.textContent = data.description;

                    // รีเซ็ตสถานะปุ่มก่อนเสมอ
                    prevBtn.classList.add('hidden');
                    nextBtn.classList.add('hidden');
                    imageCounter.classList.add('hidden');

                    // แสดงรูปภาพ
                    updateImage();

                    // ตรวจสอบจำนวนรูปและแสดงปุ่มเฉพาะเมื่อมีรูปตั้งแต่ 2 รูปขึ้นไป
                    if (currentStepImages.length >= 2) {
                        prevBtn.classList.remove('hidden');
                        nextBtn.classList.remove('hidden');
                        imageCounter.classList.remove('hidden');
                        totalImagesSpan.textContent = currentStepImages.length;
                        updateNavigationButtons();

                        console.log(`Step ${stepId}: แสดงปุ่มสไลด์ (มี ${currentStepImages.length} รูป)`);
                    } else {
                        console.log(`Step ${stepId}: ซ่อนปุ่มสไลด์ (มี ${currentStepImages.length} รูป)`);
                    }

                    popupOverlay.classList.remove('hidden');
                }
            });
        });

        function updateImage() {
            if (currentStepImages.length > 0) {
                popupImage.src = currentStepImages[currentImageIndex];
                currentImageSpan.textContent = currentImageIndex + 1;

                // อัพเดทสถานะปุ่มเมื่อเปลี่ยนรูป (เฉพาะเมื่อมีรูปมากกว่า 1 รูป)
                if (currentStepImages.length >= 2) {
                    updateNavigationButtons();
                } else {
                    // ให้แน่ใจว่าปุ่มซ่อนเมื่อมีรูปเดียว
                    prevBtn.classList.add('hidden');
                    nextBtn.classList.add('hidden');
                    imageCounter.classList.add('hidden');
                }
            }
        }

        // ฟังก์ชันสำหรับอัพเดทสถานะปุ่มนำทาง
        function updateNavigationButtons() {
            // รีเซ็ตสถานะปุ่มก่อน
            prevBtn.style.opacity = '1';
            prevBtn.style.cursor = 'pointer';
            nextBtn.style.opacity = '1';
            nextBtn.style.cursor = 'pointer';

            // ปุ่ม Previous - ลดความโปร่งใสเมื่ออยู่รูปแรก
            if (currentImageIndex === 0) {
                prevBtn.style.opacity = '0.5';
                prevBtn.style.cursor = 'not-allowed';
            }

            // ปุ่ม Next - ลดความโปร่งใสเมื่ออยู่รูปสุดท้าย
            if (currentImageIndex === currentStepImages.length - 1) {
                nextBtn.style.opacity = '0.5';
                nextBtn.style.cursor = 'not-allowed';
            }
        }

        prevBtn.addEventListener('click', () => {
            // ตรวจสอบว่าสามารถไปรูปก่อนหน้าได้หรือไม่ และมีรูปมากกว่า 1 รูป
            if (currentImageIndex > 0 && currentStepImages.length >= 2) {
                currentImageIndex--;
                updateImage();
            }
        });

        nextBtn.addEventListener('click', () => {
            // ตรวจสอบว่าสามารถไปรูปถัดไปได้หรือไม่ และมีรูปมากกว่า 1 รูป
            if (currentImageIndex < currentStepImages.length - 1 && currentStepImages.length >= 2) {
                currentImageIndex++;
                updateImage();
            }
        });

        closePopupBtn.addEventListener('click', () => {
            popupOverlay.classList.add('hidden');
        });

        popupOverlay.addEventListener('click', (e) => {
            if (e.target === popupOverlay) {
                popupOverlay.classList.add('hidden');
            }
        });
    </script>

</body>
</html>
